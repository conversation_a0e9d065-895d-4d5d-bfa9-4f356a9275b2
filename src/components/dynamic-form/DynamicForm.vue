<template>
  <div>
    <!-- 表单内容 -->
    <div class="dynamic-form-content">
      <Form
        ref="dynamicForm"
        :model="formData"
        :rules="formRules"
        :label-width="labelWidth"
        :label-position="labelPosition"
        :label-colon="labelColon"
        v-bind="formProps"
        :key="formKey"
      >
        <!-- 多节配置渲染 -->
        <template v-if="processedConfig.length > 0">
          <div
            v-for="(section, sectionIndex) in processedConfig"
            :key="sectionIndex"
            class="dynamic-form-section"
          >
            <div
              class="section-title"
              @click="toggleSection(section, sectionIndex)"
              v-if="section.title && section.showTitle !== false"
            >
              <Icon
                v-if="section.titleIcon"
                :type="section.titleIcon.type"
                :size="section.titleIcon.size || 24"
                :color="section.titleIcon.color || '#2b5fda'"
              />
              {{ section.title }}
              <Icon
                v-if="section.collapsible"
                type="ios-arrow-down"
                class="collapse-icon"
                :class="{ collapsed: section.collapsed }"
              />
            </div>
            <div
              class="section-content"
              :class="{ collapsed: section.collapsed }"
            >
              <div class="dynamic-form-fields">
                <Row :gutter="16">
                  <Col
                    v-for="field in section.fields"
                    :key="field.key"
                    :span="field.span"
                    :offset="field.offset"
                    v-show="!field.hidden"
                  >
                    <FormItem
                      :label="field.label"
                      :prop="field.key"
                      :class="field.className"
                      :style="field.style"
                      :label-width="section.labelWidth"
                    >
                      <!-- 动态渲染表单组件 -->
                      <template v-if="mode === 'view'">
                        <!-- 查看模式：显示只读文本 -->
                        <span class="field-display-value">
                          {{ getDisplayValue(field, formData[field.key]) }}
                        </span>
                      </template>
                      <template v-else>
                        <!-- 编辑模式：渲染表单组件 -->
                        <component
                          :is="getFieldComponent(field)"
                          v-model="formData[field.key]"
                          v-bind="getFieldProps(field)"
                          v-on="getFieldEvents(field)"
                          :disabled="getFieldDisabledState(field, section)"
                          @selectRoom="selectRoom"
                        />
                      </template>

                      <!-- 帮助文本 -->
                      <div v-if="field.help" class="field-help">
                        {{ field.help }}
                      </div>
                    </FormItem>
                  </Col>
                </Row>
              </div>
            </div>
          </div>
        </template>
      </Form>

      <!-- 智能显示内部操作按钮 -->
      <div
        v-if="shouldShowInternalActions && internalActions.length > 0"
        class="dynamic-form-actions"
      >
        <Button
          v-for="action in internalActions"
          :key="action.text"
          :type="action.type || 'default'"
          :loading="action.loading"
          :disabled="action.disabled"
          :icon="action.icon"
          v-bind="action.props"
          @click="handleInternalActionClick(action)"
        >
          {{ action.text }}
        </Button>
      </div>
    </div>
  </div>
</template>

<script>
import { FIELD_TYPES, FORM_MODES } from "./types";
import {
  calculateFieldDisabledState,
  formatOptions,
  formatSubmitData,
  generateInitialData,
  getDefaultSpan,
  isEmpty,
  mergeFieldConfig,
  mergeFormConfig,
  processDependency,
} from "./utils";

export default {
  name: "DynamicForm",
  props: {
    // 表单配置 - 支持数组格式（多节配置）和对象格式（单节配置，兼容旧版本）
    config: {
      type: [Array, Object],
      default: () => [],
    },
    // 表单数据
    value: {
      type: Object,
      default: () => ({}),
    },
    // 表单模式
    mode: {
      type: String,
      default: FORM_MODES.CREATE,
      validator: (value) => Object.values(FORM_MODES).includes(value),
    },
    // 是否生成底部操作按钮配置（用于bsp-layout）
    generateActions: {
      type: Boolean,
      default: false,
    },
    // 是否强制显示内部操作按钮
    forceShowActions: {
      type: Boolean,
      default: false,
    },
    // 自定义操作按钮（内部使用）
    actions: {
      type: Array,
      default: () => [],
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false,
    },
    // 是否加载中
    loading: {
      type: Boolean,
      default: false,
    },
    // 提交配置
    submitConfig: {
      type: Object,
      default: () => ({}),
    },
    // 提交URL
    submitUrl: {
      type: String,
      default: "",
    },
    // 提交方法
    submitMethod: {
      type: String,
      default: "POST",
    },
    // 是否自动提交
    autoSubmit: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      formData: {},
      formRules: {},
      sectionStates: {},
      submitLoading: false,
      layoutHasActions: false, // 外部布局是否有操作按钮
      layoutDetected: false, // 是否检测到布局环境
      formKey: Date.now(), // 用于强制重新渲染表单
    };
  },
  computed: {
    // 处理后的配置数组
    processedConfig() {
      // 兼容两种格式：数组格式和对象格式
      let configArray = [];

      if (Array.isArray(this.config)) {
        // 新的数组格式
        configArray = this.config;
      } else if (this.config && typeof this.config === "object") {
        // 旧的对象格式，转换为数组格式
        configArray = [this.config];
      }

      return configArray.map((section) => {
        // 直接使用原始section对象，确保响应式更新正常工作
        // 只处理字段配置，其他属性保持原样
        const mergedSection = mergeFormConfig(section);

        // 将合并后的配置应用到原始section上（除了fields）
        Object.keys(mergedSection).forEach((key) => {
          if (key !== "fields" && !section.hasOwnProperty(key)) {
            this.$set(section, key, mergedSection[key]);
          }
        });

        // 实时处理字段配置和依赖关系
        const processedFields = mergedSection.fields.map((field) => {
          const merged = mergeFieldConfig(field, mergedSection);
          // 设置默认span
          if (!merged.span) {
            merged.span = getDefaultSpan(mergedSection.columns || 3);
          }
          // 实时处理依赖关系 - 这里会因为 formData 的变化而重新计算
          return processDependency(merged, this.formData);
        });

        // 直接设置fields，确保响应式更新
        section.fields = processedFields;

        return section;
      });
    },

    // 所有字段的扁平化数组（用于生成初始数据和验证规则）
    allFields() {
      const fields = [];
      this.processedConfig.forEach((section) => {
        if (section.fields) {
          fields.push(...section.fields);
        }
      });
      return fields;
    },

    // 表单属性（使用第一个section的配置作为全局配置）
    globalConfig() {
      return this.processedConfig.length > 0 ? this.processedConfig[0] : {};
    },
    title() {
      return this.globalConfig.title;
    },
    showTitle() {
      return this.globalConfig.showTitle;
    },
    titleIcon() {
      return this.globalConfig.titleIcon;
    },
    labelWidth() {
      return this.globalConfig.labelWidth;
    },
    labelPosition() {
      return this.globalConfig.labelPosition;
    },
    labelColon() {
      return this.globalConfig.labelColon;
    },
    formProps() {
      return this.globalConfig.formProps;
    },

    // 模式样式类
    modeClass() {
      return `${this.mode}-mode`;
    },

    // 是否在布局中（简化判断）
    inLayout() {
      return this.$attrs["in-layout"] !== undefined || this.layoutDetected;
    },

    // 合并后的提交配置
    mergedSubmitConfig() {
      const defaultConfig = {
        validateBeforeSubmit: true,
        successMessage: "提交成功",
        errorMessage: "提交失败",
        beforeSubmit: null,
        afterSubmit: null,
        transformData: null,
      };
      return { ...defaultConfig, ...this.submitConfig };
    },

    // 生成bsp-layout兼容的操作按钮配置
    bspActions() {
      if (!this.generateActions) {
        return [];
      }

      const actions = [];

      // 查看模式不显示提交按钮
      if (this.mode === FORM_MODES.VIEW) {
        return actions;
      }

      // 添加默认的提交按钮
      if (this.mergedSubmitConfig.showInActions) {
        actions.push({
          name: "submit",
          label: this.mergedSubmitConfig.text,
          type: this.mergedSubmitConfig.type,
          icon: "ios-send",
          loading: this.submitLoading,
          disabled: false,
        });
      }

      return actions;
    },

    // 是否应该显示内部操作按钮
    shouldShowInternalActions() {
      // 强制显示内部按钮
      if (this.forceShowActions) {
        return true;
      }

      // 如果没有检测到布局环境，显示内部按钮
      if (!this.layoutDetected) {
        return true;
      }

      // 如果检测到布局环境但布局没有配置操作按钮，显示内部按钮
      if (this.layoutDetected && !this.layoutHasActions) {
        return true;
      }

      // 查看模式不显示操作按钮
      if (this.mode === FORM_MODES.VIEW) {
        return false;
      }

      return false;
    },

    // 内部操作按钮配置（完全依赖外部配置）
    internalActions() {
      if (!this.shouldShowInternalActions) {
        return [];
      }

      // 只使用外部传入的actions配置
      return this.actions || [];
    },
  },
  watch: {
    value: {
      handler(newVal) {
        this.formData = { ...this.formData, ...newVal };
      },
      deep: true,
      immediate: true,
    },

    config: {
      handler() {
        this.initForm();
      },
      deep: true,
      immediate: true,
    },

    // 监听表单数据变化，实时更新依赖关系
    formData: {
      handler(newVal, oldVal) {
        // 如果 oldVal 不存在（初始化时），跳过检查
        if (!oldVal) {
          return;
        }

        // 检查是否有依赖字段发生变化
        const hasDependendFieldChanged = this.allFields.some((field) => {
          if (field.dependsOn) {
            const dependKey = field.dependsOn.key;
            return newVal[dependKey] !== oldVal[dependKey];
          }
          return false;
        });

        // 如果有依赖字段变化，强制重新计算 processedConfig 并清除隐藏字段的验证错误
        if (hasDependendFieldChanged) {
          console.log("=== 依赖字段变化，清除隐藏字段验证错误 ===");
          this.$forceUpdate();

          // 在下一个 tick 清除隐藏字段的验证错误
          this.$nextTick(() => {
            this.clearHiddenFieldsValidation();
          });
        }
      },
      deep: true,
    },
  },
  created() {
    this.initForm();
  },

  mounted() {
    this.$nextTick(() => {
      this.detectLayoutEnvironment();
    });
  },

  updated() {
    this.$nextTick(() => {
      this.detectLayoutEnvironment();
    });
  },
  methods: {
    // 初始化表单
    initForm() {
      // 生成初始数据
      const initialData = generateInitialData(this.allFields);
      this.formData = { ...initialData, ...this.value };

      // 生成验证规则
      this.generateRules();

      // 初始化分组状态
      this.initSectionStates();
    },

    // 生成验证规则
    generateRules() {
      const rules = {};

      this.allFields.forEach((field) => {
        // 为所有字段生成验证规则，不管是否隐藏
        // 在验证时再动态判断哪些字段需要校验
        const mergedField = mergeFieldConfig(field, {});

        // 为所有配置了规则的字段添加验证规则
        if (mergedField.rules && mergedField.rules.length > 0) {
          rules[field.key] = mergedField.rules;
        }
      });

      this.formRules = rules;
      console.log("=== 验证规则生成 ===");
      console.log("生成的验证规则:", rules);
      console.log("规则字段数量:", Object.keys(rules).length);

      // 强制重新渲染表单以应用新的验证规则
      this.formKey = Date.now();
    },

    // 初始化分组状态
    initSectionStates() {
      const states = {};
      this.processedConfig.forEach((section, index) => {
        const key = section.title || `section-${index}`;
        states[key] = {
          collapsed: section.collapsed || false,
        };
      });
      this.sectionStates = states;
    },

    // 简化的布局检测（通过props传入）
    detectLayoutEnvironment() {
      // 简化逻辑：通过props或事件来判断，而不是复杂的DOM检测
      // 如果外部明确传入了布局信息，使用外部信息
      if (this.$attrs["in-layout"] !== undefined) {
        this.layoutDetected = true;
        this.layoutHasActions = this.$attrs["layout-has-actions"] === "true";
      } else {
        // 默认假设独立使用
        this.layoutDetected = false;
        this.layoutHasActions = false;
      }

      // 触发事件通知外部组件
      this.$emit("layout-detected", {
        detected: this.layoutDetected,
        hasActions: this.layoutHasActions,
        shouldShowInternal: this.shouldShowInternalActions,
      });
    },

    // 获取字段的禁用状态（应用优先级规则）
    getFieldDisabledState(field, section) {
      return calculateFieldDisabledState(
        field,
        section,
        this.disabled,
        this.mode
      );
    },
    selectRoom(data,value){
        console.log(data,value,'data,valuedata,value')
    },

    // 获取字段组件
    getFieldComponent(field) {
      console.log(field,'field')
      if (field.render) {
        return {
          functional: true,
          render: (h, ctx) => {
            // 为render函数提供完整的字段配置，包括处理后的events
            const fieldWithEvents = {
              ...field,
              events: this.getFieldEvents(field)
            };
            return field.render(h, ctx.props.value, ctx.parent.formData, fieldWithEvents);
          }
        };
      }

      // 字典组件特殊处理
      if (field.type === FIELD_TYPES.DICTIONARY) {
        return {
          functional: true,
          render: (h, ctx) => {
            return h("s-dicgrid", {
              props: {
                value: ctx.props.value,
                dicName: field.dicName || field.dicCode,
                appMark: field.appMark || serverConfig.APP_CODE || "",
                multiple: field.multiple || false,
                ...ctx.props,
              },
              on: ctx.listeners,
            });
          },
        };
      }

      // Select 组件特殊处理 - 需要渲染 Option 子组件
      if (field.type === FIELD_TYPES.SELECT) {
        return {
          functional: true,
          render: (h, ctx) => {
            const options = field.options ? formatOptions(field.options) : [];
            const optionNodes = options.map((option) => {
              return h(
                "Option",
                {
                  props: {
                    value: option.value,
                    label: option.label,
                    disabled: option.disabled,
                  },
                  key: option.value,
                },
                option.label
              );
            });

            return h(
              "Select",
              {
                props: ctx.props,
                on: ctx.listeners,
              },
              optionNodes
            );
          },
        };
      }

      // RadioGroup 组件特殊处理 - 需要渲染 Radio 子组件
      if (field.type === FIELD_TYPES.RADIO) {
        return {
          functional: true,
          render: (h, ctx) => {
            const options = field.options ? formatOptions(field.options) : [];
            const radioNodes = options.map((option) => {
              return h(
                "Radio",
                {
                  props: {
                    label: option.value,
                    disabled: option.disabled,
                  },
                  key: option.value,
                },
                option.label
              );
            });

            return h(
              "RadioGroup",
              {
                props: ctx.props,
                on: ctx.listeners,
              },
              radioNodes
            );
          },
        };
      }

      // CheckboxGroup 组件特殊处理 - 需要渲染 Checkbox 子组件
      if (field.type === FIELD_TYPES.CHECKBOX) {
        return {
          functional: true,
          render: (h, ctx) => {
            const options = field.options ? formatOptions(field.options) : [];
            const checkboxNodes = options.map((option) => {
              return h(
                "Checkbox",
                {
                  props: {
                    label: option.value,
                    disabled: option.disabled,
                  },
                  key: option.value,
                },
                option.label
              );
            });

            return h(
              "CheckboxGroup",
              {
                props: ctx.props,
                on: ctx.listeners,
              },
              checkboxNodes
            );
          },
        };
      }

      const componentMap = {
        [FIELD_TYPES.INPUT]: "Input",
        [FIELD_TYPES.TEXTAREA]: "Input",
        [FIELD_TYPES.DATE_PICKER]: "DatePicker",
        [FIELD_TYPES.TIME_PICKER]: "TimePicker",
        [FIELD_TYPES.DATETIME_PICKER]: "DatePicker",
        [FIELD_TYPES.DATE_TIME_PICKER]: "DatePicker",
        [FIELD_TYPES.NUMBER]: "InputNumber",
        [FIELD_TYPES.INPUT_NUMBER]: "InputNumber",
        [FIELD_TYPES.SWITCH]: "i-switch",
        [FIELD_TYPES.RATE]: "Rate",
        [FIELD_TYPES.SLIDER]: "Slider",
        [FIELD_TYPES.UPLOAD]: "Upload",
        [FIELD_TYPES.FILE_UPLOAD]: "file-upload",
        [FIELD_TYPES.BSP_FILE_UPLOAD]: "bsp-file-upload",
      };

      return componentMap[field.type] || "Input";
    },

    // 获取字段属性
    getFieldProps(field) {
      // 使用 mergeFieldConfig 来确保应用了默认属性
      const mergedField = mergeFieldConfig(field, {});
      const props = { ...mergedField.props };

      // 处理特殊类型的属性
      switch (field.type) {
        case FIELD_TYPES.TEXTAREA:
          props.type = "textarea";
          break;
        case FIELD_TYPES.DATETIME_PICKER:
        case FIELD_TYPES.DATE_TIME_PICKER:
          props.type = "datetime";
          // 确保日期时间选择器100%宽度
          props.style = { width: "100%", ...props.style };
          break;
        case FIELD_TYPES.DATE_PICKER:
          // 确保日期选择器100%宽度
          props.style = { width: "100%", ...props.style };
          break;
        case FIELD_TYPES.TIME_PICKER:
          // 确保时间选择器100%宽度
          props.style = { width: "100%", ...props.style };
          break;
        // SELECT, RADIO, CHECKBOX 的 options 现在在 getFieldComponent 中处理
        case FIELD_TYPES.SELECT:
        case FIELD_TYPES.RADIO:
        case FIELD_TYPES.CHECKBOX:
          // options 已在 getFieldComponent 中处理，这里不需要额外处理
          break;
        case FIELD_TYPES.INPUT_NUMBER:
        case FIELD_TYPES.NUMBER:
          // 确保数字输入框100%宽度
          props.style = { width: "100%", ...props.style };
          break;
        case FIELD_TYPES.BSP_FILE_UPLOAD:
          // 将 v-model 的 value 映射到组件的 default-list prop
          if (this.formData[field.key]) {
            let defaultList = this.formData[field.key];
            // 如果是字符串，尝试解析为数组
            if (typeof defaultList === 'string') {
              try {
                defaultList = JSON.parse(defaultList);
              } catch (e) {
                console.warn('BSP_FILE_UPLOAD: 无法解析 defaultList 字符串:', defaultList);
                defaultList = [];
              }
            }
            // 确保是数组类型
            props.defaultList = Array.isArray(defaultList) ? defaultList : [];
          } else {
            props.defaultList = [];
          }
          break;
      }

      return props;
    },

    // 获取字段事件
    getFieldEvents(field) {
      const events = { ...field.events };
      console.log(events,'events')
      // 保存原始事件处理函数
      const originalChange = events.change;
      const originalInput = events.input;
      const originalSelectSuccess = events['on-select-success'];
      const originalSelectRoom = events['selectRoom'];
      const changeHandler = (value) => {
        // 对日期时间类型进行特殊处理
        let processedValue = value;
        if (
          field.type === FIELD_TYPES.DATE_TIME_PICKER ||
          field.type === FIELD_TYPES.DATETIME_PICKER ||
          field.type === FIELD_TYPES.DATE_PICKER
        ) {
          // 如果是 Date 对象，转换为字符串
          if (value instanceof Date) {
            const format =
              (field.props && field.props.format) || "yyyy-MM-dd HH:mm:ss";
            processedValue = this.formatDate(value, format);
          }
        }

        this.$set(this.formData, field.key, processedValue);
        this.$emit("input", this.formData);
        this.$emit("field-change", field.key, processedValue, this.formData);

        if (originalChange) {
          originalChange(processedValue, this.formData, field);
        }
      };

      // 为输入框类型添加 input 事件监听（实时响应输入变化）
      const inputHandler = (value) => {
        this.$set(this.formData, field.key, value);
        this.$emit("input", this.formData);
        this.$emit("field-change", field.key, value, this.formData);

        if (originalInput) {
          originalInput(value, this.formData, field);
        }
      };

      events.change = changeHandler;

      // 为输入框类型添加 input 事件（实时响应）
      if (
        field.type === FIELD_TYPES.INPUT ||
        field.type === FIELD_TYPES.TEXTAREA ||
        field.type === FIELD_TYPES.NUMBER ||
        field.type === FIELD_TYPES.INPUT_NUMBER
      ) {
        events.input = inputHandler;
      }

      // 为日期时间组件添加 on-change 事件（iView 特有）
      if (
        field.type === FIELD_TYPES.DATE_TIME_PICKER ||
        field.type === FIELD_TYPES.DATETIME_PICKER ||
        field.type === FIELD_TYPES.DATE_PICKER ||
        field.type === FIELD_TYPES.TIME_PICKER
      ) {
        events["on-change"] = changeHandler;
      }

      // 为 Select 组件添加 on-change 事件（iView 特有）
      if (field.type === FIELD_TYPES.SELECT) {
        events["on-change"] = changeHandler;
      }

      // 为 Radio 和 Checkbox 组件添加 on-change 事件（iView 特有）
      if (
        field.type === FIELD_TYPES.RADIO ||
        field.type === FIELD_TYPES.CHECKBOX
      ) {
        events["on-change"] = changeHandler;
      }

      // 为 Switch 组件添加 on-change 事件（iView 特有）
      if (field.type === FIELD_TYPES.SWITCH) {
        events["on-change"] = changeHandler;
      }

      // 为 Rate 组件添加 on-change 事件（iView 特有）
      if (field.type === FIELD_TYPES.RATE) {
        events["on-change"] = changeHandler;
      }

      // 为 Slider 组件添加 on-change 事件（iView 特有）
      if (field.type === FIELD_TYPES.SLIDER) {
        events["on-change"] = changeHandler;
      }

      // 为 InputNumber 组件添加 on-change 事件（iView 特有）
      if (
        field.type === FIELD_TYPES.NUMBER ||
        field.type === FIELD_TYPES.INPUT_NUMBER
      ) {
        events["on-change"] = changeHandler;
      }

      // 为 bsp-file-upload 组件添加特殊事件处理
      if (field.type === FIELD_TYPES.BSP_FILE_UPLOAD) {
        // 上传成功事件
        events["on-success"] = (response, file, fileList) => {
          console.log("bsp-file-upload success", fileList);
          // 直接将 iView Upload 返回的 fileList 同步到 formData
          this.$set(this.formData, field.key, fileList);
          this.$emit("input", this.formData);
          this.$emit("field-change", field.key, fileList, this.formData);

          // 如果有外部定义的 on-success，也执行它
          if (field.events && field.events["on-success"]) {
            field.events["on-success"](response, file, fileList);
          }
        };
        // 删除文件事件
        events["on-remove"] = (file, fileList) => {
          console.log("bsp-file-upload remove", fileList);
          // 直接将 iView Upload 返回的 fileList 同步到 formData
          this.$set(this.formData, field.key, fileList);
          this.$emit("input", this.formData);
          this.$emit("field-change", field.key, fileList, this.formData);

          // 如果有外部定义的 on-remove，也执行它
          if (field.events && field.events["on-remove"]) {
            field.events["on-remove"](file, fileList);
          }
        };
      }
       // 为 CUSTOM 添加特殊事件处理
      if (field.type === FIELD_TYPES.CUSTOM) {
         events["on-change"] = changeHandler;
         events["on-select-success"] = (selectedData, value) => {
          console.log(selectedData, value, 'CUSTOM on-select-success')
          // 调用changeHandler更新formData
          changeHandler(value)

          // 如果有外始定义的 on-select-success，也执行它
          if (originalSelectSuccess) {
            originalSelectSuccess.call(this, selectedData, value);
          }
        }
        events["selectRoom"] = (selectedData, value) => {
          console.log(selectedData, value, 'CUSTOM selectRoom')
          // 调用changeHandler更新formData
          changeHandler(value)

          // 如果有原始定义的 selectRoom，也执行它
          if (originalSelectRoom) {
            originalSelectRoom.call(this, selectedData, value);
          }
        }
      }

      return events;
    },

    // 获取显示值（查看模式）
    getDisplayValue(field, value) {
      if (isEmpty(value)) {
        return "暂无数据";
      }

      switch (field.type) {
        case FIELD_TYPES.DICTIONARY:
          // 字典类型在查看模式下显示原始值，实际项目中可以通过字典服务获取显示文本
          return value;
        case FIELD_TYPES.SELECT:
        case FIELD_TYPES.RADIO:
          if (field.options) {
            const option = formatOptions(field.options).find(
              (opt) => opt.value === value
            );
            return option ? option.label : value;
          }
          return value;
        case FIELD_TYPES.CHECKBOX:
          if (Array.isArray(value) && field.options) {
            const options = formatOptions(field.options);
            return value
              .map((val) => {
                const option = options.find((opt) => opt.value === val);
                return option ? option.label : val;
              })
              .join(", ");
          }
          return Array.isArray(value) ? value.join(", ") : value;
        case FIELD_TYPES.SWITCH:
          return value ? "是" : "否";
        case FIELD_TYPES.DATE_PICKER:
        case FIELD_TYPES.DATETIME_PICKER:
        case FIELD_TYPES.DATE_TIME_PICKER:
        case FIELD_TYPES.TIME_PICKER:
          return value
            ? typeof value === "string"
              ? value
              : value.toLocaleString()
            : "";
        case FIELD_TYPES.FILE_UPLOAD:
          if (Array.isArray(value) && value.length > 0) {
            return value
              .map((file) => file.fileName || file.name || "文件")
              .join(", ");
          }
          return "暂无文件";
        default:
          return value;
      }
    },

    // 切换分组折叠状态
    toggleSection(section, sectionIndex) {
      console.log("=== toggleSection 调试信息 ===");
      console.log("1. 方法被调用，参数:", {
        sectionTitle: section.title,
        sectionIndex,
        collapsible: section.collapsible,
        currentCollapsed: section.collapsed,
      });

      if (section.collapsible) {
        console.log("2. 分组可折叠，开始切换状态");
        const oldCollapsed = section.collapsed;

        // 同时更新原始配置和当前section
        const originalSection = Array.isArray(this.config)
          ? this.config[sectionIndex]
          : this.config;

        // 更新当前section（模板使用的对象）
        this.$set(section, "collapsed", !section.collapsed);

        // 同时更新原始配置（确保数据一致性）
        if (originalSection) {
          this.$set(originalSection, "collapsed", section.collapsed);
        }

        console.log("3. 状态切换完成:", {
          from: oldCollapsed,
          to: section.collapsed,
        });

        // 同时更新状态记录
        const key = section.title || `section-${sectionIndex}`;
        this.$set(this.sectionStates, key, {
          collapsed: section.collapsed,
        });
        console.log("4. 状态记录已更新:", this.sectionStates[key]);

        // 强制重新渲染
        this.$forceUpdate();

        // 检查DOM更新
        this.$nextTick(() => {
          console.log("5. DOM更新完成，当前状态:", section.collapsed);
          const sectionElement = document.querySelector(
            `.dynamic-form-section:nth-child(${
              sectionIndex + 1
            }) .section-content`
          );
          if (sectionElement) {
            console.log("6. DOM元素类名:", sectionElement.className);
            console.log(
              "7. DOM元素显示状态:",
              window.getComputedStyle(sectionElement).display
            );
          } else {
            console.log("6. 未找到对应的DOM元素");
          }
        });
      } else {
        console.log("2. 分组不可折叠，collapsible =", section.collapsible);
      }
      console.log("=== toggleSection 调试信息结束 ===");
    },

    // 处理表单验证事件（已移除自动校验监听）
    // handleValidate方法已移除，因为我们只在手动提交时进行校验

    // 处理bsp-layout的操作按钮点击
    handleBspAction(action) {
      const actionName = action.name || action;

      // 触发action事件，让父组件处理
      this.$emit("action", action);

      // 如果是提交按钮，执行内置提交逻辑
      if (actionName === "submit") {
        this.handleInternalSubmit();
      }
    },

    // 处理内部操作按钮点击
    handleInternalActionClick(action) {
      if (action.onClick) {
        action.onClick(this.formData, this);
      } else {
        // 默认处理逻辑
        this.$emit("action", action);
      }
    },

    // 表单验证
    validate(callback) {
      console.log("=== 开始表单验证 ===");

      // 获取当前隐藏字段列表
      const hiddenFields = this.getHiddenFields();
      const allFieldKeys = this.allFields.map((f) => f.key);
      const visibleFields = allFieldKeys.filter(
        (key) => !hiddenFields.includes(key)
      );

      console.log("所有字段:", allFieldKeys);
      console.log("隐藏字段:", hiddenFields);
      console.log("显示字段:", visibleFields);
      console.log("当前表单数据:", this.formData);
      console.log("当前验证规则:", this.formRules);

      // 在验证前，先清除隐藏字段的验证错误
      try {
        this.clearHiddenFieldsValidation();
      } catch (error) {
        console.warn("清除隐藏字段验证时出错:", error);
      }

      // 检查表单引用是否存在
      if (!this.$refs.dynamicForm) {
        const error = new Error("DynamicForm: 表单引用不存在");
        console.error(error);

        if (callback) {
          callback(false, { form: [{ message: "表单引用不存在" }] });
          return;
        }
        return Promise.resolve(false);
      }

      if (callback) {
        return this.$refs.dynamicForm.validate((valid, errors) => {
          console.log("原始验证结果:", { valid, errors });

          // 如果原始验证就通过了，直接返回通过
          if (valid) {
            console.log("原始验证通过，直接返回 true");
            callback(true, null);
            return;
          }

          // 如果原始验证失败，需要进一步判断
          if (errors) {
            // 有具体错误信息，过滤隐藏字段的错误
            const filteredErrors = this.filterHiddenFieldErrors(errors);
            const isValid =
              !filteredErrors || Object.keys(filteredErrors).length === 0;

            console.log("过滤后验证结果:", { isValid, filteredErrors });
            callback(isValid, filteredErrors);
          } else {
            // 没有具体错误信息，但验证失败
            // 这种情况下需要手动检查是否只是隐藏字段导致的验证失败
            console.log("验证失败但无错误信息，手动检查隐藏字段");
            const isValidAfterHiddenCheck = this.validateVisibleFieldsOnly();
            console.log("手动检查结果:", isValidAfterHiddenCheck);
            callback(isValidAfterHiddenCheck, null);
          }
        });
      }

      // 返回 Promise
      return new Promise((resolve, reject) => {
        this.$refs.dynamicForm.validate((valid, errors) => {
          console.log("原始验证结果:", { valid, errors });

          // 如果原始验证就通过了，直接返回通过
          if (valid) {
            console.log("原始验证通过，直接返回 true");
            resolve(true);
            return;
          }

          // 如果原始验证失败，需要进一步判断
          if (errors) {
            // 有具体错误信息，过滤隐藏字段的错误
            const filteredErrors = this.filterHiddenFieldErrors(errors);
            const isValid =
              !filteredErrors || Object.keys(filteredErrors).length === 0;

            console.log("过滤后验证结果:", { isValid, filteredErrors });
            resolve(isValid);
          } else {
            // 没有具体错误信息，但验证失败
            // 这种情况下需要手动检查是否只是隐藏字段导致的验证失败
            console.log("验证失败但无错误信息，手动检查隐藏字段");
            const isValidAfterHiddenCheck = this.validateVisibleFieldsOnly();
            console.log("手动检查结果:", isValidAfterHiddenCheck);
            resolve(isValidAfterHiddenCheck);
          }
        });
      });
    },

    // 验证指定字段
    validateField(prop, callback) {
      return this.$refs.dynamicForm.validateField(prop, callback);
    },

    // 重置表单
    resetFields() {
      this.$refs.dynamicForm.resetFields();
      this.initForm();
    },

    // 清除验证
    clearValidate(props) {
      if (!this.$refs.dynamicForm) {
        return;
      }

      const formRef = this.$refs.dynamicForm;

      // iView 的 resetFields 方法（重置整个表单）
      if (!props && typeof formRef.resetFields === "function") {
        formRef.resetFields();
      } else {
        console.warn(
          "DynamicForm: iView 不支持清除单个字段的验证，建议使用 resetFields() 重置整个表单"
        );
      }
    },

    // 清除隐藏字段的验证错误
    clearHiddenFieldsValidation() {
      if (!this.$refs.dynamicForm) {
        console.warn("DynamicForm: dynamicForm ref not found");
        return;
      }

      const hiddenFields = this.getHiddenFields();
      if (hiddenFields.length > 0) {
        console.log("清除隐藏字段验证:", hiddenFields);

        // 对于 iView，我们采用不同的策略：
        // 1. 清空隐藏字段的值
        // 2. 不依赖 clearValidate 方法，而是在验证时过滤隐藏字段
        hiddenFields.forEach((fieldKey) => {
          if (this.formData.hasOwnProperty(fieldKey)) {
            // 清空隐藏字段的值，这样它们就不会有验证错误
            this.$set(this.formData, fieldKey, "");
          }
        });

        console.log("已清空隐藏字段的值:", hiddenFields);
      }
    },

    // 获取隐藏字段列表
    getHiddenFields() {
      const hiddenFields = [];

      this.allFields.forEach((field) => {
        // 处理依赖关系，获取字段的实际状态
        const processedField = processDependency(field, this.formData);

        if (processedField.hidden) {
          hiddenFields.push(field.key);
        }
      });

      return hiddenFields;
    },

    // 过滤隐藏字段的验证错误
    filterHiddenFieldErrors(errors) {
      if (!errors) {
        console.log("没有验证错误，返回 null");
        return null;
      }

      const hiddenFields = this.getHiddenFields();
      const filteredErrors = {};
      const hiddenFieldErrors = {};

      console.log("=== 过滤验证错误 ===");
      console.log("原始错误:", errors);
      console.log("隐藏字段:", hiddenFields);

      Object.keys(errors).forEach((key) => {
        if (hiddenFields.includes(key)) {
          // 记录被过滤掉的隐藏字段错误
          hiddenFieldErrors[key] = errors[key];
        } else {
          // 保留非隐藏字段的错误
          filteredErrors[key] = errors[key];
        }
      });

      console.log("隐藏字段错误 (已过滤):", hiddenFieldErrors);
      console.log("显示字段错误 (保留):", filteredErrors);

      return Object.keys(filteredErrors).length > 0 ? filteredErrors : null;
    },

    // 手动验证只有显示字段
    validateVisibleFieldsOnly() {
      console.log("=== 手动验证显示字段 ===");

      const hiddenFields = this.getHiddenFields();
      const visibleFields = this.allFields.filter(
        (field) => !hiddenFields.includes(field.key)
      );

      console.log(
        "需要验证的显示字段:",
        visibleFields.map((f) => f.key)
      );

      // 检查每个显示字段是否满足验证要求
      for (const field of visibleFields) {
        const mergedField = mergeFieldConfig(field, {});
        const value = this.formData[field.key];

        console.log(`检查字段 ${field.key}:`, {
          value,
          required: mergedField.required,
          rules: mergedField.rules,
        });

        // 检查必填字段
        if (
          mergedField.required &&
          (value === "" || value === null || value === undefined)
        ) {
          console.log(`字段 ${field.key} 必填但为空，验证失败`);
          return false;
        }

        // 检查其他验证规则
        if (mergedField.rules && mergedField.rules.length > 0) {
          for (const rule of mergedField.rules) {
            if (
              rule.required &&
              (value === "" || value === null || value === undefined)
            ) {
              console.log(`字段 ${field.key} 规则要求必填但为空，验证失败`);
              return false;
            }
            // 可以在这里添加更多规则检查
          }
        }
      }

      console.log("所有显示字段验证通过");
      return true;
    },

    // 获取表单数据
    getFormData() {
      return formatSubmitData(this.formData, this.allFields);
    },

    // 设置表单数据
    setFormData(data) {
      Object.keys(data).forEach((key) => {
        this.$set(this.formData, key, data[key]);
      });
      this.$emit("input", this.formData);
    },

    // 设置字段值
    setFieldValue(key, value) {
      this.$set(this.formData, key, value);
      this.$emit("input", this.formData);
      this.$emit("field-change", key, value, this.formData);
    },

    // 获取字段值
    getFieldValue(key) {
      return this.formData[key];
    },

    // 格式化日期
    formatDate(date, format) {
      if (!date || !(date instanceof Date)) {
        return date;
      }

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");

      return format
        .replace("yyyy", year)
        .replace("MM", month)
        .replace("dd", day)
        .replace("HH", hours)
        .replace("mm", minutes)
        .replace("ss", seconds);
    },

    // 内置提交处理
    handleInternalSubmit() {
      const config = this.mergedSubmitConfig;

      // 执行提交前钩子
      if (config.beforeSubmit) {
        const result = config.beforeSubmit(this.formData, this);
        if (result === false) {
          return; // 阻止提交
        }
      }

      // 验证表单
      if (config.validateBeforeSubmit) {
        this.validate((valid) => {
          if (valid) {
            this.performSubmit();
          } else {
            this.$Message.error("请填写完整的表单信息");
          }
        });
      } else {
        this.performSubmit();
      }
    },

    // 执行提交
    async performSubmit() {
      const config = this.mergedSubmitConfig;
      this.submitLoading = true;

      try {
        let submitData = this.getFormData();

        // 数据转换
        if (config.transformData) {
          submitData = config.transformData(submitData, this.formData, this);
        }

        // 触发提交前事件
        this.$emit("before-submit", submitData, this);

        let result = null;

        // 如果有提交URL，使用内置的HTTP请求
        if (this.submitUrl) {
          result = await this.httpSubmit(submitData);
        } else {
          // 否则触发submit事件，让父组件处理
          this.$emit("submit", submitData, this);
          result = { success: true };
        }

        this.submitLoading = false;

        // 处理提交结果
        if (result && result.success !== false) {
          // 显示成功消息
          if (config.successMessage) {
            this.$Message.success(config.successMessage);
          }

          // 触发成功事件
          this.$emit("submit-success", result, submitData, this);

          // 执行提交后钩子
          if (config.afterSubmit) {
            config.afterSubmit(result, submitData, this);
          }
        } else {
          // 处理失败情况
          const errorMsg = (result && result.message) || config.errorMessage;
          if (errorMsg) {
            this.$Message.error(errorMsg);
          }

          this.$emit("submit-error", result, submitData, this);
        }
      } catch (error) {
        this.submitLoading = false;
        console.error("表单提交失败:", error);

        const errorMsg = error.message || config.errorMessage;
        if (errorMsg) {
          this.$Message.error(errorMsg);
        }

        this.$emit("submit-error", error, this.getFormData(), this);
      }
    },

    // HTTP提交
    async httpSubmit(data) {
      if (!this.$store || !this.$store.dispatch) {
        throw new Error("需要Vuex store支持");
      }

      const requestConfig = {
        url: this.submitUrl,
        params: data,
      };

      // 根据提交方法选择不同的action
      const actionName =
        this.submitMethod.toUpperCase() === "GET"
          ? "authGetRequest"
          : "authPostRequest";

      return await this.$store.dispatch(actionName, requestConfig);
    },

    // 快速提交方法（供外部调用）
    submit() {
      this.handleInternalSubmit();
    },

    // 工具方法
    isEmpty,
  },
};
</script>

<style lang="less" scoped>
.dynamic-form-content {
  .dynamic-form-section {
    .section-title {
      display: flex;
      align-items: center;
      cursor: pointer;
      border-bottom: 1px solid #cee0f0;
      background: #eff6ff;
      line-height: 40px;
      padding-left: 10px;
      font-family: Source Han Sans CN, Source Han Sans CN;
      font-weight: bold;
      font-size: 16px;
      color: #00244a;
      margin-bottom: 16px;

      .ivu-icon {
        &:not(.collapse-icon) {
          margin-right: 10px;
        }
      }

      .collapse-icon {
        margin-left: auto;
        transition: transform 0.2s ease-in-out;
        &.collapsed {
          transform: rotate(-180deg);
        }
      }
    }

    .section-content {
      &.collapsed {
        display: none;
      }
    }
  }

  // 查看模式样式
  .field-display-value {
    display: inline-block;
    min-height: 32px;
    line-height: 32px;
    color: #515a6e;
    word-break: break-all;

    &:empty::before {
      content: "暂无数据";
      color: #c5c8ce;
    }
  }

  // 帮助文本样式
  .field-help {
    font-size: 12px;
    color: #80848f;
    margin-top: 4px;
    line-height: 1.5;
  }
}
</style>
